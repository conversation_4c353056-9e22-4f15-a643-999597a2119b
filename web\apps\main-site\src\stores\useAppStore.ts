/**
 * Main Application Store - Global UI and App State
 * 
 * This store manages application-wide state that doesn't belong to specific features.
 * It replaces several React Context providers for better performance.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
interface AppState {
  // UI State
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  compactView: boolean;
  
  // Loading States
  globalLoading: boolean;
  loadingStates: Record<string, boolean>;
  
  // Error Handling
  errors: Array<{
    id: string;
    message: string;
    type: 'error' | 'warning' | 'info';
    timestamp: number;
  }>;
  
  // Feature Flags
  betaFeatures: boolean;
  
  // Navigation
  breadcrumbs: Array<{
    label: string;
    href?: string;
  }>;
}

interface AppActions {
  // UI Actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setTheme: (theme: AppState['theme']) => void;
  setCompactView: (compact: boolean) => void;
  
  // Loading Actions
  setGlobalLoading: (loading: boolean) => void;
  setLoading: (key: string, loading: boolean) => void;
  
  // Error Actions
  addError: (message: string, type?: AppState['errors'][0]['type']) => void;
  removeError: (id: string) => void;
  clearErrors: () => void;
  
  // Feature Flags
  setBetaFeatures: (enabled: boolean) => void;
  
  // Navigation
  setBreadcrumbs: (breadcrumbs: AppState['breadcrumbs']) => void;
  addBreadcrumb: (breadcrumb: AppState['breadcrumbs'][0]) => void;
}

type AppStore = AppState & AppActions;

// Store Implementation
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial State
        sidebarOpen: false,
        theme: 'dark',
        compactView: false,
        globalLoading: false,
        loadingStates: {},
        errors: [],
        betaFeatures: false,
        breadcrumbs: [],

        // UI Actions
        toggleSidebar: () =>
          set((state) => {
            state.sidebarOpen = !state.sidebarOpen;
          }),

        setSidebarOpen: (open) =>
          set((state) => {
            state.sidebarOpen = open;
          }),

        setTheme: (theme) =>
          set((state) => {
            state.theme = theme;
          }),

        setCompactView: (compact) =>
          set((state) => {
            state.compactView = compact;
          }),

        // Loading Actions
        setGlobalLoading: (loading) =>
          set((state) => {
            state.globalLoading = loading;
          }),

        setLoading: (key, loading) =>
          set((state) => {
            if (loading) {
              state.loadingStates[key] = true;
            } else {
              delete state.loadingStates[key];
            }
          }),

        // Error Actions
        addError: (message, type = 'error') =>
          set((state) => {
            const id = `error-${Date.now()}-${Math.random()}`;
            state.errors.push({
              id,
              message,
              type,
              timestamp: Date.now(),
            });
          }),

        removeError: (id) =>
          set((state) => {
            state.errors = state.errors.filter((error) => error.id !== id);
          }),

        clearErrors: () =>
          set((state) => {
            state.errors = [];
          }),

        // Feature Flags
        setBetaFeatures: (enabled) =>
          set((state) => {
            state.betaFeatures = enabled;
          }),

        // Navigation
        setBreadcrumbs: (breadcrumbs) =>
          set((state) => {
            state.breadcrumbs = breadcrumbs;
          }),

        addBreadcrumb: (breadcrumb) =>
          set((state) => {
            state.breadcrumbs.push(breadcrumb);
          }),
      })),
      {
        name: 'bank-of-styx-app-store',
        partialize: (state) => ({
          theme: state.theme,
          compactView: state.compactView,
          betaFeatures: state.betaFeatures,
          sidebarOpen: state.sidebarOpen,
        }),
      }
    ),
    {
      name: 'AppStore',
    }
  )
);

// Selectors for Performance Optimization
export const useAppSelectors = {
  // UI Selectors
  sidebarOpen: () => useAppStore((state) => state.sidebarOpen),
  theme: () => useAppStore((state) => state.theme),
  compactView: () => useAppStore((state) => state.compactView),
  
  // Loading Selectors
  globalLoading: () => useAppStore((state) => state.globalLoading),
  isLoading: (key: string) => useAppStore((state) => state.loadingStates[key] || false),
  anyLoading: () => useAppStore((state) => 
    state.globalLoading || Object.keys(state.loadingStates).length > 0
  ),
  
  // Error Selectors
  errors: () => useAppStore((state) => state.errors),
  hasErrors: () => useAppStore((state) => state.errors.length > 0),
  
  // Feature Selectors
  betaFeatures: () => useAppStore((state) => state.betaFeatures),
  
  // Navigation Selectors
  breadcrumbs: () => useAppStore((state) => state.breadcrumbs),
};

// Actions for external use
export const useAppActions = () => useAppStore((state) => ({
  toggleSidebar: state.toggleSidebar,
  setSidebarOpen: state.setSidebarOpen,
  setTheme: state.setTheme,
  setCompactView: state.setCompactView,
  setGlobalLoading: state.setGlobalLoading,
  setLoading: state.setLoading,
  addError: state.addError,
  removeError: state.removeError,
  clearErrors: state.clearErrors,
  setBetaFeatures: state.setBetaFeatures,
  setBreadcrumbs: state.setBreadcrumbs,
  addBreadcrumb: state.addBreadcrumb,
}));
